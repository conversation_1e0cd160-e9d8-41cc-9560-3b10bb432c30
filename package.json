{"name": "tavoos-vue3", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "run-p type-check build-only", "preview": "vite preview --port 4173", "test:unit": "vitest --environment jsdom", "test:e2e": "start-server-and-test preview http://localhost:4173/ 'cypress open --e2e'", "test:e2e:ci": "start-server-and-test preview http://localhost:4173/ 'cypress run --e2e'", "build-only": "vite build", "type-check": "vue-tsc -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"pinia": "^3.0.3", "vue": "^3.5.16", "vue-router": "^4.5.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.11.0", "@tailwindcss/nesting": "^0.0.0-insiders.565cd3e", "@tailwindcss/postcss": "^4.1.8", "@types/jsdom": "^21.1.7", "@types/node": "^22.15.30", "@types/vue": "^2.0.0", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "cypress": "^14.4.1", "eslint": "^9.10.0", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-vue": "^10.2.0", "jsdom": "^26.1.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.4", "postcss-import": "^16.1.0", "prettier": "^3.5.3", "start-server-and-test": "^2.0.12", "tailwindcss": "^4.1.8", "typescript": "~5.8.3", "vite": "^6.3.5", "vitest": "^3.2.2", "vue-tsc": "^2.2.10"}}