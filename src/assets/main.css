@import "./base.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    user-select: none;
  }

  a, button, select {
    cursor: pointer;
  }

  body {
    background-color: var(--color-body-background);
    color: white;
  }

  #app {
    font-weight: normal;
  }

  .router-link-active {
    @apply secondary pointer-events-none rounded-md;
  }

  .arabic {
    direction: rtl;
    line-height: 225%;

    &.quran {
      font-family: "nabi";
    }

    &.dua {
      font-family: "nabi";
      font-size: 125%;
    }

    &.title {
      font-family: "estedad-semibold";
    }
  }

  .farsi {
    direction: rtl;
    font-family: "estedad-medium";
  }

  .icon {
    @apply h-6 md:h-8 aspect-square;
  }

  .tab-title {
    font-family: estedad;
  }

  h1.farsi {
    font-family: "farsi-title";
  }

  .rtl {
    direction: rtl;
  }

  .ltr {
    direction: ltr;
  }

  .primary {
    background-color: var(--color-primary-background-color);
    color: var(--color-primary-text-color);
  }

  .secondary {
    background-color: var(--color-secondary-background-color);
    color: var(--color-secondary-text-color);
  }

  body:not(:has(.touch-screen)) button:hover {
    background-color: var(--color-primary-hover-background-color);
    border-color: var(--color-primary-hover-text-color);
    color: var(--color-primary-hover-text-color);
  }

  button[data-toggle="active"] {
    outline-color: var(--color-primary-hover-background-color);
    outline-style: solid;
    outline-width: 0.125rem;
    outline-offset: -0.125rem;
  }

  button:active {
    background-color: initial;
    border-color: initial;
    color: initial;
  }

  li:not(.passive) {
    cursor: pointer;
  }

  li:not(.passive):hover {
    background-color: var(--color-primary-hover-background-color);
    color: var(--color-primary-hover-text-color);
  }

  /* ol:not(.non-separated) > li:not(:first-child):not(.selected),
  ul:not(.non-separated) > li:not(:first-child):not(.selected) { */
  ol:not(.non-separated) > li:not(:first-child):not(.selected),
  ul:not(.non-separated) > li:not(:first-child):not(.selected) {
    border-top-style: dashed;
    border-top-width: thin;
    border-color: black;
  }

  ol:not(.non-separated) > li.selected,
  ul:not(.non-separated) > li.selected {
    border-top-style: dashed;
    border-bottom-style: dashed;
    border-top-width: thin;
    border-bottom-width: thin;
    border-color: black;
  }

  .touch-screen {
    ol, ul {
      align-content: flex-end;
    }
  }

  /* ol:not(.non-separated) > li:not(:first-child).selected + li,
  ul:not(.non-separated) > li:not(:first-child).selected + li {
    border-top: none;
  } */

  @supports selector(::webkit-scrollbar) {
    ::webkit-scrollbar {
      width: 2em;
    }

    ::webkit-scrollbar-track {
      background: black;
      border-radius: 100vw;
      margin-block: 0.5em;
    }

    ::webkit-scrollbar-thumb {
      background: silver;
      border-radius: 100vw;
    }

    ::webkit-scrollbar-thumb:hover {
      background: white;
    }
  }

  @supports (scrollbar-color: red blue) {
    * {
      scrollbar-color: var(--color-primary-foreground-color) var(--color-primary-background-color);
    }
  }
}

@layer component {
  .app-section-menu-component {
    ul > a > li {
      @apply flex gap-4 items-center;

      & svg {
        @apply w-4;
      }
    }

    #hamburger-menu-icon.router-link-exact-active {
      background: none;
      fill: #fbbf24;
    }

    #hamburger-menu-content li {
      @apply p-2 rounded-md transition-all duration-150;
    }

    #hamburger-menu-content li:not(.router-link-exact-active) {
      @apply  hover:bg-tavoos-orange hover:text-black;
    }
  }

  .circular-progressbar {
    &::before {
      content: '';

      @apply absolute inset-0 rounded-full z-50;

      background-image: conic-gradient(
        var(--color) var(--progress, 0),
        rgba(0, 0, 0, 0) var(--progress, 0)
      );

      --hole-size: 60%;

      -webkit-mask: radial-gradient(circle at center, transparent var(--hole-size), black var(--hole-size));
      mask: radial-gradient(circle at center, transparent var(--hole-size), black var(--hole-size));
    }
  }

  .landscape-navigation-component {
    .navigation-button {
      @apply w-8 lg:w-10 absolute top-1/2 -translate-y-1/2 transition-transform aspect-square dark:secondary;
    }

    .page-data {
      @apply absolute top-0 text-center transition-transform text-xl dark:secondary;
      color: var(--color-primary-text-color);
    }

    .page-number {
      @apply absolute bottom-0 text-center transition-transform primary dark:secondary;
      /* color: var(--color-primary-text-color); */
    }
  }

  .memory-game {
    #memory-game-progress-bar {
      transform-origin: left;
      animation: grow 2s linear;
    }

    @keyframes grow {
      from {
        transform: scaleX(0);
      }

      to {
        transform: scaleX(1);
      }
    }
  }

  .mobile-app-view {
    @media (min-width: 1024px) {
      .about {
        min-height: 100vh;
        display: flex;
        align-items: center;
      }
    }

    a {
      @apply rounded-lg w-36 h-16 p-4 border-2 border-white/0 hover:border-white font-black text-center
    }
  }

  .modal-component {
    dialog::backdrop {
      @apply backdrop-blur-sm;
    }
  }

  .portrait-navigation-component {
    .shortcut-button {
      @apply w-16 rounded-full p-1 text-center font-thin transition-all;
    }
  }

  .quran-reciter-settings-component {
    input[type='checkbox'],
    input[type='radio'] {
      @apply w-6 aspect-square;
    }

    label {
      @apply flex gap-2 items-center;

      * {
        @apply p-2;
      }
    }

    legend {
      @apply mb-4;
    }

    select {
      @apply h-8 w-28;
    }
  }

  .quran-view {
    .navigation-close-button::before,
    .navigation-close-button::after {
      content: '';
      width: 0.25rem;
      height: 60%;
      border-radius: 0.25rem;
      position: absolute;
      top: 50%;
      left: 50%;
      background-color: white;
      transition: all 1s ease;
    }

    .navigation-close-button::before {
      transform: translate(-50%, -50%) rotate(-45deg);
    }
    .navigation-close-button::after {
      transform: translate(-50%, -50%) rotate(45deg);
    }

    .navigation-close-button.idle::before {
      transform: translate(-50%, -50%) rotate(45deg) scale(50%);
    }
    .navigation-close-button.idle::after {
      transform: translate(-50%, -50%) rotate(-45deg) scale(50%);
    }
    .page-photo {
      @apply flex-1 h-full bg-contain bg-no-repeat;
    }
  }

  .social-media-view {
    @media (min-width: 1024px) {
      .about {
        min-height: 100vh;
        display: flex;
        align-items: center;
      }
    }

    a {
      @apply rounded-lg w-36 h-16 p-4 border-2 border-white/0 hover:border-white font-black text-center
    }
  }

  .verse-end-sign-menu-item-component {
    .menu-item {
      &.left-side {
        left: calc(var(--ampltitude) * var(--radius) * cos(var(--angle)));
      }

      &.right-side {
        right: calc(var(--ampltitude) * var(--radius) * cos(var(--angle)));
      }

      &.left-side,
      &.right-side {
        top: calc(var(--ampltitude) * var(--radius) * sin(var(--angle)));
        @apply -translate-y-1/2;
      }

      &.bottom-right-side,
      &.top-right-side {
        left: calc(var(--ampltitude) * 2rem);
      }

      &.bottom-left-side,
      &.top-left-side {
        right: calc(var(--ampltitude) * 3rem);
      }

      &.bottom-right-side,
      &.bottom-left-side {
        --top-offset: calc((var(--total-items) - var(--index) - 1) * (3.5rem + var(--vertical-gap)));

        top: calc(var(--ampltitude) * var(--top-offset));
      }

      &.top-right-side,
      &.top-left-side {
        --bottom-offset: calc(var(--index) * (3.5rem + var(--vertical-gap)) + 2rem);

        bottom: calc(var(--ampltitude) * var(--bottom-offset));
      }

      transition-duration: var(--transition-duration);

      &::after {
        content: attr(label);

        /* Secondary */
        background-color: var(--color-secondary-background-color);
        color: var(--color-secondary-text-color);

        @apply block whitespace-nowrap px-2 py-1 rounded absolute top-1/2 -translate-y-1/2 transition-all duration-300 ease-out;
      }

      &.left-side,
      &.bottom-left-side,
      &.top-left-side {
        &::after {
          @apply right-20 opacity-0;
        }

        &:hover::after {
          @apply right-14 opacity-100;
        }
      }

      &.right-side,
      &.bottom-right-side,
      &.top-right-side {
        &::after {
          @apply left-20 opacity-0;
        }

        &:hover::after {
          @apply left-14 opacity-100;
        }
      }
    }
  }
}

@layer utilities {
  /* Styles according to Bootstrap */
  .text-primary {

  }

  .text-secondary {

  }

  .text-success {

  }

  .text-danger {

  }

  .text-warning {

  }

  .text-info {

  }

  .text-light {

  }

  .text-dark {

  }

  .text-body {

  }

  .text-muted {

  }

  /* Styles according to myself */
  .secondary {
    .text-highlight {
      color:moccasin;
    }
  }

  .primary {
    .text-highlight {
      color: var(--color-highlight);
    }
  }
}
