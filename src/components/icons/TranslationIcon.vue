<script setup lang="ts">
const props = defineProps<{
  width?: number;
  height?: number;
}>();
</script>

<template>
  <!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
  <svg
    :height="props.height ?? props.width ?? '100%'"
    :width="props.width ?? props.height ?? '100%'"
    class="fill-current"
    viewBox="0 0 48 48"
    version="1.1"
  >
    <circle
      class="a"
      cx="24"
      cy="24"
      r="21.5"
    />
    <line
      class="a"
      x1="30.6916"
      y1="44.4321"
      x2="17.3084"
      y2="3.5679"
    />
    <polyline
      class="a"
      points="22.429 36.637 16.773 19.235 10.9 36.637"
    />
    <line
      class="a"
      x1="12.8577"
      y1="30.7637"
      x2="20.471"
      y2="30.7637"
    />
    <path
      class="a"
      d="M27.2363,11.1588c-.0324,4.5431-.26,11.6173,5.16,17.3286"
    />
    <path
      class="a"
      d="M22.9238,13.9955a38.5878,38.5878,0,0,0,15.4107-1.7334"
    />
    <path
      class="a"
      d="M23.7854,23.3448c2.5858-4.3938,13.61-7.906,14.5491-.7309a5.5319,5.5319,0,0,1-2.1877,5.17"
    />
    <path
      class="a"
      d="M35.4464,16.0588c.1685,5.1934-6.1155,13.58-10.1157,12.0113"
    />
  </svg>
</template>

<style>
.a {
  fill: none;
  stroke: #000000;
  stroke-linecap: round;
  stroke-linejoin: round;
}
</style>
