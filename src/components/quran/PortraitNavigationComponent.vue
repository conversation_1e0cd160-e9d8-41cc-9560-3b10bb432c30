<script setup lang="ts">
import NextPageButton from '@/components/quran/NextPageButton.vue';
import PreviousPageButton from '@/components/quran/PreviousPageButton.vue';
import { computed } from 'vue';
import { IChapter, IVerse } from '@/types/types-quran.ts';
import PopupIcon from '@/components/icons/PopupIcon.vue';

const emit = defineEmits(['change', 'navigate', 'openChaptersList']);
const props = defineProps<{
  pageContent?: IVerse[];
  number: number;
}>();

const chapters = computed(() => {
  try {
    return [...new Set(props.pageContent?.map(c => c.chapter))];
  } catch (exc) {
    return [];
  }
});

function onPageNumberInput(e: Event) {
  const input = e.currentTarget as HTMLInputElement;

  navigateToPage(input.value);
}

function navigateToPage(pageNumber: number | string) {
  emit('change', `${pageNumber}`);
}
</script>

<template>
  <div class="flex gap-4 items-center w-fit rounded-b-xl rounded-xl px-4 py-2 secondary rtl">
    <button
      class="secondary transition-transform  flex gap-2 items-center"
      @click.stop="$emit('openChaptersList', props.pageContent?.[0]?.chapter)"
    >
      <PopupIcon
        class="w-6 text-transparent"
        stroke-color="white"
      />

      <span class="arabic min-w-[2rem] text-start">
        {{ chapters.map((chapter: IChapter) => chapter.title).join('، ') }}
      </span>
    </button>

    <button
      @click.stop="navigateToPage(1)"
      class="shortcut-button hidden lg:inline"
      :class="{ 'translate-y-full opacity-0': props.number === 1 }"
    >
      1
    </button>

    <div class="flex gap-2">
      <PreviousPageButton
        @click.stop="$emit('navigate', 'backward')"
        class="icon titled on-right"
        title="Previous page"
      />

      <input
        step="1"
        :value="props.number"
        @change="onPageNumberInput"
        class="w-20 lg:w-auto h-6 md:h-8 px-2 py-4 rounded-lg [&_*]:text-center secondary font-black text-2xl bg-red-500"
        max="604"
        min="1"
        type="number"
      />

      <NextPageButton
        @click.stop="$emit('navigate', 'forward')"
        class="icon titled on-left"
        title="Next page"
      />
    </div>

    <button
      @click.stop="navigateToPage(604)"
      class="shortcut-button hidden lg:inline"
      :class="{ 'translate-y-full opacity-0': props.number === 604 }"
    >
      604
    </button>
  </div>
</template>

<style scoped>
.shortcut-button {
  @apply w-16 rounded-full p-1 text-center font-thin transition-all;
}
</style>
