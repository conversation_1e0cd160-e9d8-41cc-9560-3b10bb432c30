<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { IVerse } from '@/types/types-quran.ts';
import VerseEndSign from '@/components/quran/VerseEndSign.vue';
import Api from '@/services/api/api';

const props = defineProps<{
  number: number;
  content: IVerse[];
}>();

const verseEndSigns = ref<(typeof VerseEndSign)[]>([]);

const PHOTOS_REPOSITORY = 'https://file-keeper.com/media/image/quran-pages-2';
const PHOTOS_REPOSITORY_BACKUP = `${new Api().host}/quran-pages`;
// const PHOTOS_REPOSITORY = `https://behnegar.app/api/v1/tavoos/quran/page/\(number)/photo`;

const isLoading = ref(true);
const isLoaded = ref(false);

const activeRepository = ref(PHOTOS_REPOSITORY);

const src = computed(() => `${activeRepository.value}/quran-${String(props.number).padStart(3, '0')}.jpg`);
// const src = computed(() => `${PHOTOS_REPOSITORY}/${props.number}/photo`);

watch(src, (newSrc, oldSrc) => {
  if (newSrc === oldSrc) return;

  isLoaded.value = false;
  isLoading.value = true;
});

function onImageLoad() {
  isLoading.value = false;
  isLoaded.value = true;
}

function onImageError() {
  activeRepository.value = PHOTOS_REPOSITORY_BACKUP;
};

function collapseOtherOpenMenus(verse: IVerse) {
  verseEndSigns.value.forEach(sign => {
    if (sign.verse.indexInQuran === verse.indexInQuran) return;

    sign.collapseMenu();
  });
}
</script>

<template>
  <div
    class="max-h-full overflow-hidden flex quran-page-photo-container relative"
    :style="{ '--blur-amount': `${8 + (Math.random() * 2 - 1)}px` }"
  >
    <img
      @load="onImageLoad"
      @error="onImageError"
      class="object-contain"
      :class="{ blurred: isLoading }"
      loading="lazy"
      :src="src"
    />

    <VerseEndSign
      v-for="verse in content"
      :key="`verse-end-sign-${verse.chapter.index}-${verse.index}`"
      :verse="verse"
      :data-verse-end-sign="verse.indexInQuran"
      ref="verseEndSigns"
      @click.stop="() => collapseOtherOpenMenus(verse)"
    />
  </div>
</template>

<style scoped>
.blurred {
  filter: blur(var(--blur-amount));
}

.quran-page-photo-container {
  aspect-ratio: 1250 / 2022;
  transition: filter 0.75s ease-out;
}
</style>
