<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { IVerse } from '@/types/types-quran.ts';
import NumberUtils from '@/utils/number-utils.ts';

interface Challenge {
  verse: IVerse;
  answers: number[];
  correctAnswer: number;
}

const props = defineProps<{ pageContent: IVerse[] }>();

const isEvaluating = ref(false);
const wasCorrect = ref(false);
const chosenAnswer = ref(-1);
const challenges = ref<Challenge[]>([]);

const currentChallenge = computed(() => [...challenges.value].pop());

function shuffled<T>(array: T[]) {
  const clone = [...array];
  const result: T[] = [];

  while (clone.length) {
    const randomIndex = Math.floor(Math.random() * clone.length);
    const [randomItem] = clone.splice(randomIndex, 1);
    result.push(randomItem);
  }

  return result;
}

function draw() {
  isEvaluating.value = false;

  const index = Math.floor(Math.random() * props.pageContent.length);

  const verse = props.pageContent[index];
  const correctAnswer = verse.index;

  const availableIndices = [...new Set(props.pageContent.map(verse => verse.index))].sort();
  const wrongIndices = availableIndices.filter(index => index !== correctAnswer);
  const wrongAnswers = shuffled(wrongIndices).splice(0, 3);

  const answers = shuffled([...wrongAnswers, correctAnswer]);

  challenges.value.push({
    verse,
    answers,
    correctAnswer,
  });
}

function evaluate(event: MouseEvent) {
  event.stopPropagation();

  isEvaluating.value = true;

  const btn = event.currentTarget as HTMLButtonElement;

  chosenAnswer.value = parseInt(btn.getAttribute('data-answer') ?? '-1');
  wasCorrect.value = chosenAnswer.value === currentChallenge.value?.correctAnswer;
}

onMounted(() => {
  draw();
});
</script>

<template>
  <div
    v-if="currentChallenge"
    class="memory-game h-full flex flex-col"
  >
    <p class="p-4 md:p-8 quran arabic flex-1 overflow-auto">
      {{ currentChallenge.verse.text }}
    </p>

    <div
      ref="progressbar"
      class="h-3"
    >
      <div
        id="memory-game-progress-bar"
        v-if="isEvaluating"
        @animationend="draw"
        class="h-full bg-white"
      />
    </div>

    <fieldset
      class="grid grid-cols-4 gap-2 md:gap-4 rtl p-4 md:p-8 bg-black/25"
      :class="{ 'pointer-events-none': isEvaluating }"
    >
      <button
        v-for="(answer, index) in currentChallenge.answers"
        :key="`challenge-verse-${currentChallenge.verse.indexInQuran}-${index}-${answer}`"
        class="border-2 rounded-lg p-2 text-xl"
        :class="{
          'bg-emerald-500 hover:bg-emerald-500': isEvaluating && answer == currentChallenge.correctAnswer,
          'bg-red-500     hover:bg-red-500':     isEvaluating && !wasCorrect && answer == chosenAnswer,
          'hover:bg-unset': isEvaluating,
        }"
        @click="evaluate"
        :data-answer="answer"
      >
        {{ NumberUtils.persianize(answer) }}
      </button>
    </fieldset>
  </div>
</template>
