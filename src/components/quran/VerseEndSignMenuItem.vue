<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { MenuItem } from '@/types/types-quran.ts';
import { Side } from '@/types/types-general.ts';

const props = defineProps<{
  active: boolean;
  item: MenuItem;
  side: Side;
  totalItems: number;
}>();

const transitionDuration = ref(0);

const startAngle = 135;
const endAngle = 225;

const amplitude = computed(() => (props.active ? 1 : 0));
const stepSize = computed(() => (endAngle - startAngle) / (props.totalItems - 1));
const angle = computed(() => startAngle + props.item.index * stepSize.value);

function updateTransitionSpeed() {
  transitionDuration.value = 100 + 500 * Math.random();
}

onMounted(updateTransitionSpeed);
</script>

<template>
  <div
    class="verse-end-sign-menu-item-component menu-item w-12 absolute cursor-pointer transition-all ease-in bg-tavoos-orange outline outline-2 hover:outline-4 outline-black rounded-full"
    :class="[`${props.side}-side`, active ? 'opacity-100' : 'opacity-0 pointer-events-none']"
    @transitionend="updateTransitionSpeed"
    :style="{
      '--width': '3rem',
      '--ampltitude': amplitude,
      '--angle': `${angle}deg`,
      '--angle-gap-half': `${stepSize / 2}deg`,
      '--index': props.item.index,
      '--radial-gap-amplitude': 1.2,
      '--radius': 'calc((var(--radial-gap-amplitude) * var(--width) / 2) / sin(var(--angle-gap-half)))',
      '--total-items': props.totalItems,
      '--transition-duration': `${transitionDuration}ms`,
      '--vertical-gap': '0.25rem',
      width: 'var(--width)',
    }"
    :index="props.item.index"
    :label="props.item.title"
  >
    <component
      :is="props.item.icon"
      class="w-full aspect-square rounded-full transition-all pointer-events-none text-black bg-none mix-blend-multiply"
    />
  </div>
</template>
