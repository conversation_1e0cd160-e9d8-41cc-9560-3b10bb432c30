<script setup lang="ts">
import { RouterView } from 'vue-router';
import ProcessMonitor from '@/components/ProcessMonitor.vue';
import AppSectionsMenu from '@/components/AppSectionsMenu.vue';
import TavoosButton from '@/components/TavoosButton.vue';
import { onMounted, ref } from 'vue';

const isRunningOnATouchDevice = ref(false);

onMounted(() => {
  isRunningOnATouchDevice.value = 'ontouchstart' in document.documentElement;

  if (isRunningOnATouchDevice.value) {
    document.querySelector('body')?.classList.add('touch-screen');
  }
});
</script>

<template>
  <aside
    class="
      relative
      flex-0
      landscape:w-fit portrait:w-full
      landscape:h-full portrait:h-fit
      flex
      landscape:flex-col portrait:flex-row
      landscape:py-2 portrait:px-2
      secondary
      shadow-lg
      landscape:rounded-r-lg portrait:rounded-b-lg
      z-50
    "
  >
    <nav
      class="w-full landscape:w-20 h-full portrait:h-16 flex-0 flex gap-2 items-center justify-between p-2 sticky top-0 left-0"
      style="flex-direction: inherit"
    >
      <TavoosButton />

      <div
        class="flex-1 overflow-auto h-full flex landscape:flex-col items-center gap-2 px-2"
        id="page-specific-tools"
      ></div>

      <AppSectionsMenu />
    </nav>
  </aside>

  <main class="flex-1 overflow-hidden relative">
    <RouterView v-slot="{ Component }">
      <Transition
        name="route"
        mode="out-in"
      >
        <component :is="Component" />
      </Transition>
    </RouterView>
  </main>

  <ProcessMonitor class="z-10" />
</template>

<style scoped>
.route-enter-active {
  transition: all 0.2s ease-out;
}
.route-leave-active {
  transition: all 0.2s ease-in;
}

.route-enter-from {
  opacity: 0;
  transform: translateX(-10rem);
}

.route-leave-to {
  opacity: 0;
  transform: translateX(7rem);
}
</style>
